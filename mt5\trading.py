"""
MetaTrader5 交易执行模块
"""
import MetaTrader5 as mt5
import random
from datetime import datetime
from mt5.connection import mt5_connection
from utils.logger import logger
from utils.helpers import safe_float

class MT5TradingEngine:
    """MT5交易执行引擎"""
    
    def __init__(self):
        self.base_magic = 12345
    
    def get_filling_mode(self, symbol):
        """
        根据交易对选择合适的订单填充方式
        采用专业交易系统的填充策略
        """
        try:
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return mt5.ORDER_FILLING_IOC
            
            # 检查支持的填充方式
            filling_modes = symbol_info.filling_mode
            
            # 智能优先级: FOK > IOC > RETURN
            if filling_modes & mt5.SYMBOL_FILLING_FOK:
                return mt5.ORDER_FILLING_FOK
            elif filling_modes & mt5.SYMBOL_FILLING_IOC:
                return mt5.ORDER_FILLING_IOC
            else:
                return mt5.ORDER_FILLING_RETURN
                
        except Exception as e:
            logger.error(f"获取填充方式失败: {e}")
            return mt5.ORDER_FILLING_IOC
    
    def get_symbol_deviation(self, symbol):
        """
        根据交易对设置滑点
        采用专业交易系统的滑点策略
        """
        try:
            if symbol.startswith(("BTC", "ETH")):
                return 50  # 加密货币较大滑点
            elif symbol.startswith("XAU"):
                return 30  # 黄金中等滑点
            else:
                return 10  # 外汇较小滑点
        except Exception:
            return 10
    
    def get_magic_number(self):
        """生成智能魔术数字"""
        return self.base_magic + random.randint(1, 999)
    
    def calculate_sl_tp_from_usd(self, symbol, direction, entry_price, lot_size, sl_usd, tp_usd):
        """
        根据美元金额计算止损止盈价格
        
        Args:
            symbol: 交易对
            direction: 交易方向 (BUY/SELL)
            entry_price: 入场价格
            lot_size: 手数
            sl_usd: 止损金额(美元)
            tp_usd: 止盈金额(美元)
        
        Returns:
            (sl_price, tp_price)
        """
        try:
            if not mt5_connection.ensure_connection():
                return 0.0, 0.0
            
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                logger.error(f"无法获取交易对信息: {symbol}")
                return 0.0, 0.0
            
            # 获取合约规格
            contract_size = symbol_info.trade_contract_size
            point = symbol_info.point
            
            # 计算每点价值
            if symbol_info.currency_profit == "USD":
                pip_value = contract_size * point * lot_size
            else:
                # 需要转换为USD，这里简化处理
                pip_value = contract_size * point * lot_size
            
            if pip_value == 0:
                return 0.0, 0.0
            
            # 计算止损止盈点数
            sl_points = sl_usd / pip_value if sl_usd > 0 else 0
            tp_points = tp_usd / pip_value if tp_usd > 0 else 0
            
            # 计算价格
            sl_price = 0.0
            tp_price = 0.0
            
            if direction == "BUY":
                if sl_points > 0:
                    sl_price = entry_price - (sl_points * point)
                if tp_points > 0:
                    tp_price = entry_price + (tp_points * point)
            else:  # SELL
                if sl_points > 0:
                    sl_price = entry_price + (sl_points * point)
                if tp_points > 0:
                    tp_price = entry_price - (tp_points * point)
            
            return sl_price, tp_price
            
        except Exception as e:
            logger.error(f"计算止损止盈价格失败: {e}")
            return 0.0, 0.0
    
    def place_order(self, symbol, direction, lot_size, sl_usd=0, tp_usd=0, comment=""):
        """
        下单函数（使用动态盈亏监控，不设置固定止盈止损价格）

        Args:
            symbol: 交易对
            direction: 交易方向 (BUY/SELL)
            lot_size: 手数
            sl_usd: 止损金额(美元) - 用于监控
            tp_usd: 止盈金额(美元) - 用于监控
            comment: 订单备注

        Returns:
            订单结果字典
        """
        try:
            if not mt5_connection.ensure_connection():
                return {"success": False, "error": "MT5连接失败"}

            # 1. 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            if not tick:
                return {"success": False, "error": f"无法获取 {symbol} 的价格信息"}

            # 2. 确定开仓价格和订单类型
            if direction == "BUY":
                price = tick.ask
                order_type = mt5.ORDER_TYPE_BUY
            else:
                price = tick.bid
                order_type = mt5.ORDER_TYPE_SELL

            # 3. 构建订单请求（不设置止盈止损价格，使用动态监控）
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": order_type,
                "price": price,
                "deviation": self.get_symbol_deviation(symbol),
                "magic": self.get_magic_number(),
                "comment": f"Bot_{comment[:20]}" if comment else "TradingBot",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": self.get_filling_mode(symbol),
            }
            
            # 5. 发送订单
            result = mt5.order_send(request)

            # 检查结果是否为None
            if result is None:
                last_error = mt5.last_error()
                return {
                    "success": False,
                    "error": f"下单失败: MT5返回None, 错误: {last_error}",
                    "retcode": -1
                }

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "error": f"下单失败: {result.comment}",
                    "retcode": result.retcode
                }
            
            # 6. 构建订单信息（确保字段名匹配交易记录保存函数的期望）
            order_info = {
                "ticket": result.order,
                "symbol": symbol,
                "direction": direction,
                "volume": lot_size,  # 匹配 _save_trade_record 期望的字段名
                "open_price": result.price,
                "sl_price": None,  # 不使用固定价格止损
                "tp_price": None,  # 不使用固定价格止盈
                "sl_usd": sl_usd,  # 止损金额
                "tp_usd": tp_usd,  # 止盈金额
                "comment": comment,
                "magic": request["magic"],
                "open_time": datetime.now()
            }

            # 7. 添加到现有的监控系统（如果设置了止盈止损金额）
            if sl_usd > 0 or tp_usd > 0:
                self._add_to_monitoring_system(result.order, sl_usd, tp_usd)

            logger.info(f"下单成功: {symbol} {direction} {lot_size}手 @ {result.price}, 订单号: {result.order}")

            # 8. 发送Bark通知
            try:
                from notifications.bark import BarkNotificationManager
                bark_manager = BarkNotificationManager()

                # 构建通知用的订单信息
                notification_order_info = {
                    "ticket": result.order,
                    "symbol": symbol,
                    "direction": direction,
                    "volume": lot_size,
                    "open_price": result.price,
                    "sl_usd": sl_usd,
                    "tp_usd": tp_usd,
                    "open_time": order_info["open_time"]
                }

                # 发送开仓通知
                bark_manager.send_trade_open_notification(notification_order_info, {})
                logger.info(f"已发送订单#{result.order}的Bark通知")

            except Exception as e:
                logger.error(f"发送Bark通知失败: {e}")

            return {
                "success": True,
                "order_ticket": result.order,
                "symbol": symbol,
                "direction": direction,
                "volume": lot_size,
                "price": result.price,
                "order_info": order_info
            }
            
        except Exception as e:
            logger.error(f"下单异常: {e}")
            return {"success": False, "error": str(e)}

    def _add_to_monitoring_system(self, ticket, sl_usd, tp_usd):
        """
        将订单添加到现有的监控系统

        Args:
            ticket: 订单号
            sl_usd: 止损金额(美元)
            tp_usd: 止盈金额(美元)
        """
        try:
            from database.manager import db_manager

            # 添加到个人订单监控规则表
            # 注意：loss_threshold应该是负数，表示亏损阈值
            loss_threshold = -abs(sl_usd) if sl_usd > 0 else None
            profit_threshold = tp_usd if tp_usd > 0 else None

            db_manager.execute_query(
                """INSERT OR REPLACE INTO individual_floating_pl_rules
                   (ticket, profit_threshold, loss_threshold, enabled, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (ticket, profit_threshold, loss_threshold, True, datetime.now(), datetime.now())
            )

            logger.info(f"订单 #{ticket} 已添加到监控系统: 止盈=${tp_usd}, 止损=${sl_usd}")

        except Exception as e:
            logger.error(f"添加订单到监控系统失败: {e}")
    
    def close_position(self, ticket, reason="手动平仓"):
        """
        平仓指定订单
        
        Args:
            ticket: 订单号
            reason: 平仓原因
        
        Returns:
            平仓结果字典
        """
        try:
            if not mt5_connection.ensure_connection():
                return {"success": False, "error": "MT5连接失败"}
            
            # 获取持仓信息
            position = mt5.positions_get(ticket=ticket)
            if not position:
                return {"success": False, "error": f"找不到订单: {ticket}"}
            
            position = position[0]
            
            # 获取当前价格
            tick = mt5.symbol_info_tick(position.symbol)
            if not tick:
                return {"success": False, "error": f"无法获取价格: {position.symbol}"}
            
            # 确定平仓价格和类型
            if position.type == mt5.POSITION_TYPE_BUY:
                close_price = tick.bid
                close_type = mt5.ORDER_TYPE_SELL
            else:
                close_price = tick.ask
                close_type = mt5.ORDER_TYPE_BUY
            
            # 构建平仓请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": close_type,
                "position": ticket,
                "price": close_price,
                "deviation": self.get_symbol_deviation(position.symbol),
                "magic": position.magic,
                "comment": f"Close_{reason}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": self.get_filling_mode(position.symbol),
            }
            
            # 发送平仓请求
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "error": f"平仓失败: {result.comment}",
                    "retcode": result.retcode
                }
            
            # 计算盈亏
            profit = position.profit + position.swap
            
            close_info = {
                "ticket": ticket,
                "symbol": position.symbol,
                "direction": "BUY" if position.type == mt5.POSITION_TYPE_BUY else "SELL",
                "volume": position.volume,
                "open_price": position.price_open,
                "close_price": close_price,
                "profit": profit,
                "reason": reason,
                "close_time": datetime.now()
            }
            
            logger.info(f"平仓成功: {ticket} {position.symbol} 盈亏: ${profit:.2f}")

            # 发送Bark平仓通知
            try:
                from notifications.bark import BarkNotificationManager
                bark_manager = BarkNotificationManager()
                bark_manager.send_trade_close_notification(close_info)
                logger.info(f"已发送订单#{ticket}的平仓Bark通知")
            except Exception as e:
                logger.error(f"发送平仓Bark通知失败: {e}")

            return {
                "success": True,
                "close_info": close_info,
                "mt5_result": result
            }
            
        except Exception as e:
            logger.error(f"平仓异常: {e}")
            return {"success": False, "error": str(e)}



# 创建全局交易引擎实例
trading_engine = MT5TradingEngine()
