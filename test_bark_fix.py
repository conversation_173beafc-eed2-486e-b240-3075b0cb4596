#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Bark通知修复
验证所有下单路径是否都包含bark通知
"""

import sys
import os
import json
from datetime import datetime

def test_bark_notification_config():
    """测试Bark通知配置"""
    print("=== 测试Bark通知配置 ===")
    
    try:
        # 检查新的通知系统
        from notifications.bark import BarkNotificationManager
        bark_manager = BarkNotificationManager()
        
        print("✅ 新的Bark通知管理器导入成功")
        
        # 测试设备密钥
        device_keys = bark_manager.get_device_keys()
        print(f"✅ 配置的设备数量: {len(device_keys)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Bark通知配置测试失败: {e}")
        return False

def test_trading_engine_notifications():
    """测试交易引擎通知"""
    print("\n=== 测试交易引擎通知 ===")
    
    try:
        from mt5.trading import trading_engine
        from notifications.bark import BarkNotificationManager
        
        print("✅ 交易引擎导入成功")
        
        # 模拟订单信息
        test_order_info = {
            "ticket": 999999,
            "symbol": "EURUSD",
            "direction": "BUY",
            "volume": 0.1,
            "open_price": 1.0850,
            "sl_usd": 100,
            "tp_usd": 200,
            "open_time": datetime.now()
        }
        
        # 测试开仓通知
        bark_manager = BarkNotificationManager()
        result = bark_manager.send_trade_open_notification(test_order_info, {})
        print(f"✅ 开仓通知测试: {'成功' if result else '失败'}")
        
        # 测试平仓通知
        test_close_info = {
            "ticket": 999999,
            "symbol": "EURUSD",
            "direction": "BUY",
            "volume": 0.1,
            "open_price": 1.0850,
            "close_price": 1.0900,
            "profit": 50.0,
            "reason": "测试平仓",
            "close_time": datetime.now()
        }
        
        result = bark_manager.send_trade_close_notification(test_close_info)
        print(f"✅ 平仓通知测试: {'成功' if result else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易引擎通知测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_vps2_notifications():
    """测试VPS2模块通知"""
    print("\n=== 测试VPS2模块通知 ===")
    
    try:
        # 检查vps2目录下的bark_notifier
        sys.path.insert(0, 'vps2')
        import bark_notifier
        
        print("✅ VPS2 bark_notifier导入成功")
        
        # 测试交易执行通知
        test_trade_info = {
            'ticket': 888888,
            'symbol': 'GBPUSD',
            'operation': 'sell',
            'volume': 0.2,
            'price': 1.2650,
            'sl': 1.2750,
            'tp': 1.2550
        }
        
        result = bark_notifier.notify_trade_execution(test_trade_info)
        print(f"✅ VPS2交易执行通知测试: {'成功' if result else '失败'}")
        
        # 测试平仓通知
        test_close_info = {
            'ticket': 888888,
            'symbol': 'GBPUSD',
            'operation': 'sell',
            'volume': 0.2,
            'open_price': 1.2650,
            'close_price': 1.2600,
            'profit': 100.0,
            'points': 50
        }
        
        result = bark_notifier.notify_trade_closed(test_close_info)
        print(f"✅ VPS2平仓通知测试: {'成功' if result else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ VPS2通知测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def check_notification_paths():
    """检查所有下单路径是否包含通知"""
    print("\n=== 检查下单路径通知覆盖 ===")
    
    paths_to_check = [
        ("mt5/trading.py", "place_order函数"),
        ("mt5/trading.py", "close_position函数"),
        ("trading/engine.py", "_execute_open_position函数"),
        ("vps2/direct_market_order_function.py", "direct_market_order函数"),
        ("vps2/mt5_trader.py", "execute_trade函数"),
    ]
    
    all_good = True
    
    for file_path, function_name in paths_to_check:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含bark通知相关代码
                has_bark_import = ('bark' in content.lower() and 
                                 ('import' in content or 'from' in content))
                has_notification_call = ('notify' in content.lower() or 
                                       'send_trade' in content.lower())
                
                if has_bark_import and has_notification_call:
                    print(f"✅ {function_name} - 包含Bark通知")
                else:
                    print(f"❌ {function_name} - 缺少Bark通知")
                    all_good = False
            else:
                print(f"⚠️  {file_path} - 文件不存在")
                
        except Exception as e:
            print(f"❌ 检查 {function_name} 失败: {e}")
            all_good = False
    
    return all_good

def main():
    """主函数"""
    print("开始测试Bark通知修复...")
    print("=" * 60)
    
    tests = [
        ("Bark通知配置", test_bark_notification_config),
        ("交易引擎通知", test_trading_engine_notifications),
        ("VPS2模块通知", test_vps2_notifications),
        ("通知路径覆盖检查", check_notification_paths),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Bark通知修复成功！")
    else:
        print("⚠️  部分测试失败，请检查相关配置和代码")

if __name__ == "__main__":
    main()
