"""
交易执行引擎 - 核心交易逻辑
"""
import time
import threading
from datetime import datetime, timedelta
from mt5.trading import trading_engine
from mt5.connection import mt5_connection
from trading.config_manager import config_manager
from database.manager import db_manager
from utils.logger import logger
from utils.helpers import safe_float

class TradingEngine:
    """交易执行引擎"""
    
    def __init__(self):
        self.position_monitors = {}  # 持仓监控字典
        self.last_trade_times = {}   # 最后交易时间记录
        self.is_running = False
        self.monitor_thread = None

        # 立即恢复监控状态，确保不丢失
        self._restore_monitoring_from_db()
    
    def start_monitoring(self):
        """启动监控线程"""
        if not self.is_running:
            self.is_running = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("交易监控线程已启动")

    def _restore_monitoring_from_db(self):
        """从数据库恢复监控状态"""
        try:
            # 查询活跃的监控记录
            active_monitors = db_manager.execute_query("""
                SELECT ticket, timeout_seconds, last_signal_time, created_at
                FROM position_monitoring
                WHERE is_active = 1
            """)

            if active_monitors:
                restored_count = 0
                for monitor in active_monitors:
                    ticket = monitor[0]
                    timeout_seconds = monitor[1]
                    last_signal_time_str = monitor[2]
                    created_at_str = monitor[3]

                    # 解析时间（支持多种格式）
                    try:
                        # 尝试ISO格式
                        if 'T' in last_signal_time_str:
                            last_signal_time = datetime.fromisoformat(last_signal_time_str.replace('Z', '+00:00'))
                        else:
                            last_signal_time = datetime.strptime(last_signal_time_str, '%Y-%m-%d %H:%M:%S.%f')
                    except ValueError:
                        # 备用格式
                        last_signal_time = datetime.strptime(last_signal_time_str, '%Y-%m-%d %H:%M:%S')

                    try:
                        created_at = datetime.strptime(created_at_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        created_at = datetime.now()

                    # 检查是否已超时
                    current_time = datetime.now()
                    time_since_signal = (current_time - last_signal_time).total_seconds()

                    if time_since_signal < timeout_seconds:
                        # 恢复到内存
                        monitor_data = {
                            "ticket": ticket,
                            "timeout_seconds": timeout_seconds,
                            "last_signal_time": last_signal_time,
                            "created_at": created_at
                        }

                        self.position_monitors[ticket] = monitor_data
                        restored_count += 1

                        logger.info(f"恢复监控: 订单#{ticket}, 剩余时间:{timeout_seconds - time_since_signal:.0f}秒")
                    else:
                        # 标记为过期
                        db_manager.execute_query(
                            "UPDATE position_monitoring SET is_active = 0 WHERE ticket = ?",
                            (ticket,)
                        )
                        logger.info(f"清理过期监控: 订单#{ticket}")

                logger.info(f"从数据库恢复了 {restored_count} 个监控记录")
            else:
                logger.info("没有需要恢复的监控记录")

        except Exception as e:
            logger.error(f"从数据库恢复监控状态失败: {e}")

    def stop_monitoring(self):
        """停止监控线程"""
        if self.is_running:
            self.is_running = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)
            logger.info("交易监控线程已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 检查持仓超时
                self._check_position_timeouts()
                
                # 检查全局止盈止损
                self._check_global_sl_tp()
                
                # 清理过期监控
                self._cleanup_expired_monitors()
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(30)  # 异常时等待30秒
    
    def process_alert(self, alert_data):
        """
        处理警报并执行交易策略
        
        Args:
            alert_data: 标准化后的警报数据
            
        Returns:
            处理结果字典
        """
        try:
            symbol = alert_data["standard_symbol"]
            signal = alert_data["standard_signal"]
            
            logger.info(f"开始处理交易信号: {symbol} {signal}")
            
            # 1. 检查紧急停止
            if self._is_emergency_stopped():
                return {"success": False, "reason": "系统紧急停止", "result_type": "系统停止"}

            # 2. 获取交易对配置
            symbol_config = config_manager.get_symbol_config(symbol)
            if not symbol_config or not symbol_config.get("enabled", False):
                return {"success": False, "reason": f"交易对 {symbol} 已禁用", "result_type": "交易对已禁用"}

            # 3. 检查现有持仓
            existing_position = self._get_existing_position(symbol)

            if existing_position is not None:
                # 有持仓 - 执行持仓管理策略（不受冷却时间限制）
                logger.info(f"检测到现有持仓，执行持仓管理: {symbol}")
                return self._execute_position_management(alert_data, symbol_config, existing_position)

            # 4. 无持仓时检查交易冷却时间
            if not self._check_trading_cooldown(symbol):
                return {"success": False, "reason": "交易冷却时间未到", "result_type": "冷却未下单"}

            # 5. 执行开仓策略
            return self._execute_open_position(alert_data, symbol_config)
                
        except Exception as e:
            logger.error(f"处理交易信号异常: {e}")
            return {"success": False, "error": str(e), "result_type": "处理异常"}
    
    def _is_emergency_stopped(self):
        """检查是否紧急停止"""
        try:
            emergency_stop = config_manager.get_system_config("emergency_stop")
            return emergency_stop == "True"
        except Exception:
            return False
    
    def _check_trading_cooldown(self, symbol):
        """检查交易冷却时间"""
        try:
            cooldown = int(config_manager.get_system_config("trading_cooldown") or 300)
            last_trade_time = self.last_trade_times.get(symbol)
            
            if last_trade_time:
                time_diff = (datetime.now() - last_trade_time).total_seconds()
                if time_diff < cooldown:
                    logger.info(f"{symbol} 交易冷却中，剩余 {cooldown - time_diff:.0f} 秒")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查交易冷却失败: {e}")
            return False
    
    def _get_existing_position(self, symbol):
        """获取现有持仓"""
        try:
            positions = mt5_connection.get_positions(symbol)
            return positions[0] if positions else None
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return None
    
    def _execute_open_position(self, alert_data, symbol_config):
        """执行开仓策略"""
        try:
            symbol = alert_data["standard_symbol"]
            signal = alert_data["standard_signal"]
            
            logger.info(f"执行开仓策略: {symbol} {signal}")
            
            # 1. 风险检查
            risk_check_result = self._perform_risk_checks(symbol, symbol_config)
            if not risk_check_result["success"]:
                return {"success": False, "reason": risk_check_result["reason"], "result_type": "风险检查失败"}

            # 2. 执行下单
            order_result = trading_engine.place_order(
                symbol=symbol,
                direction=signal,
                lot_size=symbol_config["lot_size"],
                sl_usd=symbol_config["stop_loss_usd"],
                tp_usd=symbol_config["take_profit_usd"],
                comment=f"Alert_{alert_data.get('alert_id', 'unknown')}"
            )
            
            if order_result["success"]:
                # 3. 记录订单信息
                order_info = order_result["order_info"]
                self._save_trade_record(order_info, alert_data)
                
                # 4. 启动持仓监控（仅对系统下单）
                self._start_position_monitoring(
                    order_info["ticket"],
                    symbol_config["signal_timeout"]
                )
                logger.info(f"系统订单#{order_info['ticket']}已自动添加超时监控")
                
                # 5. 更新最后交易时间
                self.last_trade_times[symbol] = datetime.now()
                
                logger.info(f"开仓成功: {symbol} {signal} 订单#{order_info['ticket']}")
                
                result_type = "下多单" if signal == "BUY" else "下空单"
                return {
                    "success": True,
                    "action": "OPEN_POSITION",
                    "order_ticket": order_info["ticket"],
                    "symbol": symbol,
                    "direction": signal,
                    "lot_size": symbol_config["lot_size"],
                    "result_type": result_type,
                    "reason": f"成功{result_type}，订单号: {order_info['ticket']}"
                }
            else:
                return {
                    "success": False,
                    "reason": f"下单失败: {order_result['error']}",
                    "result_type": "下单失败"
                }
                
        except Exception as e:
            logger.error(f"开仓策略执行失败: {e}")
            return {"success": False, "error": str(e), "result_type": "开仓异常"}
    
    def _execute_position_management(self, alert_data, symbol_config, existing_position):
        """执行持仓管理策略"""
        try:
            symbol = alert_data["standard_symbol"]
            signal = alert_data["standard_signal"]
            # 使用字符串类型的持仓方向进行比较
            position_direction = existing_position["type_str"]

            logger.info(f"执行持仓管理: {symbol} 持仓:{position_direction} 信号:{signal}")

            if signal == position_direction:
                # 信号方向一致 - 继续持有
                return self._execute_hold_position(alert_data, symbol_config, existing_position)
            else:
                # 信号方向相反 - 平仓并开新仓
                return self._execute_reverse_position(alert_data, symbol_config, existing_position)
                
        except Exception as e:
            logger.error(f"持仓管理策略执行失败: {e}")
            return {"success": False, "error": str(e), "result_type": "持仓管理异常"}
    
    def _execute_hold_position(self, alert_data, symbol_config, existing_position):
        """继续持有策略"""
        try:
            ticket = existing_position["ticket"]
            symbol = alert_data["standard_symbol"]
            
            logger.info(f"继续持有策略: {symbol} 订单#{ticket}")

            # 重置超时计时器（保持原有超时时间，只更新最后信号时间）
            if ticket in self.position_monitors:
                # 保持原有的超时时间
                original_timeout = self.position_monitors[ticket]["timeout_seconds"]
                self._reset_position_timeout(ticket, original_timeout)
            else:
                # 如果没有监控，使用配置中的默认值
                self._reset_position_timeout(ticket, symbol_config["signal_timeout"])
            
            position_type = existing_position["type_str"]
            result_type = "维持买入" if position_type == "BUY" else "维持做空"
            return {
                "success": True,
                "action": "HOLD_POSITION",
                "order_ticket": ticket,
                "symbol": symbol,
                "result_type": result_type,
                "reason": f"收到同方向确认信号，{result_type}，订单号: {ticket}"
            }
            
        except Exception as e:
            logger.error(f"持有策略执行失败: {e}")
            return {"success": False, "error": str(e), "result_type": "持有策略异常"}
    
    def _execute_reverse_position(self, alert_data, symbol_config, existing_position):
        """反向持仓策略"""
        try:
            old_ticket = existing_position["ticket"]
            symbol = alert_data["standard_symbol"]
            new_signal = alert_data["standard_signal"]
            
            logger.info(f"反向持仓策略: {symbol} 平仓#{old_ticket} 开新仓{new_signal}")
            
            # 1. 平仓现有持仓
            close_result = trading_engine.close_position(
                old_ticket,
                reason="收到反向信号，执行平仓"
            )
            
            if not close_result["success"]:
                return {
                    "success": False,
                    "reason": f"平仓失败: {close_result['error']}",
                    "result_type": "平仓失败"
                }
            
            # 2. 停止旧持仓监控
            self._stop_position_monitoring(old_ticket)
            
            # 3. 等待平仓完成
            time.sleep(1)
            
            # 4. 开新仓
            new_order_result = self._execute_open_position(alert_data, symbol_config)
            
            if new_order_result["success"]:
                result_type = "下多单" if new_signal == "BUY" else "下空单"
                return {
                    "success": True,
                    "action": "REVERSE_POSITION",
                    "closed_ticket": old_ticket,
                    "new_ticket": new_order_result["order_ticket"],
                    "symbol": symbol,
                    "new_direction": new_signal,
                    "result_type": result_type,
                    "reason": f"平仓#{old_ticket}后{result_type}，新订单号: {new_order_result['order_ticket']}"
                }
            else:
                return {
                    "success": False,
                    "reason": f"开新仓失败: {new_order_result['reason']}",
                    "result_type": "开新仓失败"
                }
                
        except Exception as e:
            logger.error(f"反向持仓策略执行失败: {e}")
            return {"success": False, "error": str(e), "result_type": "反向持仓异常"}
    
    def _perform_risk_checks(self, symbol, symbol_config):
        """执行风险检查"""
        try:
            # 检查是否启用风险控制
            enable_risk_control = config_manager.get_system_config("enable_risk_control")
            if enable_risk_control == "False":
                logger.info("风险控制已禁用，跳过风险检查")
                return {"success": True}

            # 检查最大并发交易数
            max_trades = int(config_manager.get_system_config("max_concurrent_trades") or 5)
            current_positions = len(mt5_connection.get_positions())

            if current_positions >= max_trades:
                logger.warning(f"达到最大并发交易数限制: {current_positions}/{max_trades}")
                return {"success": False, "reason": "达到最大并发交易数限制"}

            # 检查账户余额
            account_info = mt5_connection.get_account_info()
            if not account_info:
                logger.error("无法获取账户信息")
                return {"success": False, "reason": "无法获取账户信息"}

            # 检查可用保证金 - 统一计算：1手需要30 USD
            required_margin = symbol_config["lot_size"] * 30.0
            if account_info["free_margin"] < required_margin:
                logger.warning(f"可用保证金不足: {account_info['free_margin']} < {required_margin} (计算: {symbol_config['lot_size']}手 × 30 USD/手)")
                return {"success": False, "reason": "余额不足"}

            return {"success": True}

        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return {"success": False, "reason": f"风险检查异常: {str(e)}"}

    def _calculate_required_margin(self, symbol, lot_size):
        """计算所需保证金"""
        try:
            # 不同交易对的每手保证金要求（USD）
            margin_per_lot = {
                "ETHUSD": 30.0,    # ETHUSD 1手需要30 USD
                "XAUUSD": 300.0,   # XAUUSD 1手需要300 USD
                "SOLUSD": 100.0,   # SOLUSD 1手需要100 USD
                "BTCUSD": 100.0,   # BTCUSD 1手需要100 USD
                "EURUSD": 50.0,    # 外汇对默认50 USD
                "GBPUSD": 50.0,
                "USDJPY": 50.0,
                "AUDUSD": 50.0,
                "USDCAD": 50.0,
                "USDCHF": 50.0,
                "NZDUSD": 50.0,
                "GBPJPY": 50.0,
                "XAGUSD": 100.0,   # 白银
                "USOIL": 100.0,    # 原油
                "ADAUSD": 20.0,    # 其他加密货币
                "XLMUSD": 20.0,
                "DOGEUSD": 20.0,
                "LINKUSD": 30.0,
                "LTCUSD": 50.0,
                "XRPUSD": 20.0,
                "BCHUSD": 50.0
            }

            # 获取该交易对的每手保证金要求，如果没有配置则使用默认值100 USD
            per_lot_margin = margin_per_lot.get(symbol, 100.0)

            # 计算总保证金需求
            required_margin = lot_size * per_lot_margin

            logger.debug(f"保证金计算: {symbol} {lot_size}手 × {per_lot_margin} USD/手 = {required_margin} USD")

            return required_margin

        except Exception as e:
            logger.error(f"计算保证金失败: {e}")
            # 出错时使用保守的默认值
            return lot_size * 100.0

    def _save_trade_record(self, order_info, alert_data):
        """保存交易记录"""
        try:
            # 构建下单原因
            symbol = order_info["symbol"]
            direction = order_info["direction"]
            signal_type = alert_data.get("standard_signal", direction)
            open_reason = f"收到{symbol} {signal_type}警报通知"

            db_manager.execute_query(
                """INSERT INTO trades (
                    alert_id, symbol, direction, lot_size, open_price,
                    stop_loss_price, take_profit_price, stop_loss_usd, take_profit_usd,
                    status, open_time, open_reason, mt5_ticket, magic_number, comment,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    alert_data.get("alert_id"),
                    order_info["symbol"],
                    order_info["direction"],
                    order_info["volume"],
                    order_info["open_price"],
                    order_info["sl_price"],
                    order_info["tp_price"],
                    order_info["sl_usd"],
                    order_info["tp_usd"],
                    "OPEN",
                    order_info["open_time"],
                    open_reason,
                    order_info["ticket"],
                    order_info["magic"],
                    order_info["comment"],
                    datetime.now(),
                    datetime.now()
                )
            )
            logger.info(f"交易记录已保存: {symbol} {direction} 订单#{order_info['ticket']}")
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")

    def _start_position_monitoring(self, ticket, timeout_seconds):
        """启动持仓监控"""
        try:
            current_time = datetime.now()

            monitor_data = {
                "ticket": ticket,
                "timeout_seconds": timeout_seconds,
                "last_signal_time": current_time,
                "created_at": current_time
            }

            self.position_monitors[ticket] = monitor_data

            # 检查数据库中是否已存在记录
            existing_record = db_manager.execute_query(
                "SELECT ticket FROM position_monitoring WHERE ticket = ?",
                (ticket,)
            )

            if existing_record:
                # 更新现有记录
                db_manager.execute_query(
                    """UPDATE position_monitoring
                       SET timeout_seconds = ?, last_signal_time = ?, is_active = 1
                       WHERE ticket = ?""",
                    (timeout_seconds, current_time, ticket)
                )
                logger.info(f"更新持仓监控: 订单#{ticket}, 超时:{timeout_seconds}秒")
            else:
                # 插入新记录
                db_manager.execute_query(
                    """INSERT INTO position_monitoring
                       (ticket, timeout_seconds, last_signal_time, is_active)
                       VALUES (?, ?, ?, ?)""",
                    (ticket, timeout_seconds, current_time, True)
                )
                logger.info(f"启动持仓监控: 订单#{ticket}, 超时:{timeout_seconds}秒")

        except Exception as e:
            logger.error(f"启动持仓监控失败: {e}")

    def _stop_position_monitoring(self, ticket):
        """停止持仓监控"""
        try:
            if ticket in self.position_monitors:
                del self.position_monitors[ticket]

            # 更新数据库
            db_manager.execute_query(
                "UPDATE position_monitoring SET is_active = FALSE WHERE ticket = ?",
                (ticket,)
            )

            logger.info(f"停止持仓监控: 订单#{ticket}")

        except Exception as e:
            logger.error(f"停止持仓监控失败: {e}")

    def _reset_position_timeout(self, ticket, timeout_seconds):
        """重置持仓超时时间"""
        try:
            current_time = datetime.now()

            logger.info(f"重置持仓超时: 订单#{ticket}, 超时时间:{timeout_seconds}秒")

            if ticket in self.position_monitors:
                old_time = self.position_monitors[ticket]["last_signal_time"]
                self.position_monitors[ticket]["last_signal_time"] = current_time
                self.position_monitors[ticket]["timeout_seconds"] = timeout_seconds
                logger.info(f"内存监控已更新: 订单#{ticket}, 从{old_time}重置到{current_time}")

            # 更新数据库
            db_manager.execute_query(
                """UPDATE position_monitoring
                   SET last_signal_time = ?, timeout_seconds = ?
                   WHERE ticket = ?""",
                (current_time, timeout_seconds, ticket)
            )

            logger.info(f"数据库监控已更新: 订单#{ticket}")

        except Exception as e:
            logger.error(f"重置持仓超时失败: {e}")

    def _stop_position_monitoring(self, ticket):
        """停止指定订单的监控"""
        try:
            # 从内存中移除
            if ticket in self.position_monitors:
                del self.position_monitors[ticket]
                logger.info(f"已从内存中移除订单#{ticket}的监控")

            # 更新数据库状态
            db_manager.execute_query(
                "UPDATE position_monitoring SET is_active = 0 WHERE ticket = ?",
                (ticket,)
            )
            logger.info(f"已停止订单#{ticket}的监控")

        except Exception as e:
            logger.error(f"停止订单#{ticket}监控失败: {e}")

    def _update_last_signal_time(self, ticket, signal_time):
        """更新最后信号时间"""
        try:
            if ticket in self.position_monitors:
                self.position_monitors[ticket]["last_signal_time"] = datetime.fromisoformat(signal_time)

            db_manager.execute_query(
                "UPDATE position_monitoring SET last_signal_time = ? WHERE ticket = ?",
                (signal_time, ticket)
            )

        except Exception as e:
            logger.error(f"更新最后信号时间失败: {e}")

    def _check_position_timeouts(self):
        """检查持仓超时"""
        try:
            current_time = datetime.now()
            timeout_tickets = []

            for ticket, monitor_data in self.position_monitors.items():
                last_signal_time = monitor_data["last_signal_time"]
                timeout_seconds = monitor_data["timeout_seconds"]

                time_diff = (current_time - last_signal_time).total_seconds()

                if time_diff > timeout_seconds:
                    timeout_tickets.append(ticket)

            # 平仓超时订单
            for ticket in timeout_tickets:
                self._close_timeout_position(ticket)

        except Exception as e:
            logger.error(f"检查持仓超时失败: {e}")

    def _close_timeout_position(self, ticket):
        """平仓超时订单"""
        try:
            close_result = trading_engine.close_position(
                ticket,
                reason="信号超时自动平仓"
            )

            if close_result["success"]:
                self._stop_position_monitoring(ticket)
                logger.info(f"超时平仓成功: 订单#{ticket}")
            else:
                logger.error(f"超时平仓失败: 订单#{ticket}, 错误: {close_result['error']}")

        except Exception as e:
            logger.error(f"超时平仓异常: {e}")

    def _check_global_sl_tp(self):
        """检查全局止盈止损"""
        try:
            portfolio_config = config_manager.get_portfolio_config()
            if not portfolio_config or not portfolio_config.get("enabled", False):
                return

            # 计算当前总盈亏
            total_pnl = self._calculate_total_pnl()

            global_sl = portfolio_config["global_stop_loss_usd"]
            global_tp = portfolio_config["global_take_profit_usd"]

            if total_pnl <= -global_sl:
                logger.warning(f"触发全局止损: 总盈亏 {total_pnl:.2f} <= -{global_sl:.2f}")
                self._close_all_positions("全局止损触发")
            elif total_pnl >= global_tp:
                logger.info(f"触发全局止盈: 总盈亏 {total_pnl:.2f} >= {global_tp:.2f}")
                self._close_all_positions("全局止盈触发")

        except Exception as e:
            logger.error(f"检查全局止盈止损失败: {e}")

    def _calculate_total_pnl(self):
        """计算总盈亏"""
        try:
            positions = mt5_connection.get_positions()
            total_pnl = sum(pos["profit"] for pos in positions)
            return total_pnl
        except Exception as e:
            logger.error(f"计算总盈亏失败: {e}")
            return 0.0

    def _close_all_positions(self, reason):
        """平仓所有订单"""
        try:
            positions = mt5_connection.get_positions()

            for position in positions:
                close_result = trading_engine.close_position(
                    position["ticket"],
                    reason=reason
                )

                if close_result["success"]:
                    self._stop_position_monitoring(position["ticket"])
                    logger.info(f"平仓成功: 订单#{position['ticket']}")
                else:
                    logger.error(f"平仓失败: 订单#{position['ticket']}")

        except Exception as e:
            logger.error(f"平仓所有订单失败: {e}")

    def _cleanup_expired_monitors(self):
        """清理过期监控"""
        try:
            # 获取当前持仓
            current_positions = mt5_connection.get_positions()
            current_tickets = {pos["ticket"] for pos in current_positions}

            # 清理不存在的持仓监控
            expired_tickets = []
            for ticket in self.position_monitors.keys():
                if ticket not in current_tickets:
                    expired_tickets.append(ticket)

            for ticket in expired_tickets:
                self._stop_position_monitoring(ticket)

        except Exception as e:
            logger.error(f"清理过期监控失败: {e}")

# 创建全局交易引擎实例
trading_strategy_engine = TradingEngine()
