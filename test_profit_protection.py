#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
盈利保护功能测试脚本
测试各种场景下的盈利保护逻辑
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'vps2'))

def setup_test_database():
    """设置测试数据库"""
    print("设置测试数据库...")
    
    # 创建测试数据库连接
    conn = sqlite3.connect('test_trading_data.db')
    c = conn.cursor()
    
    # 创建测试表
    c.execute("""
        CREATE TABLE IF NOT EXISTS total_floating_pl_rules (
            id INTEGER PRIMARY KEY DEFAULT 1,
            profit_threshold DECIMAL(10,2),
            loss_threshold DECIMAL(10,2),
            target_profit_threshold DECIMAL(10,2),
            protection_profit_threshold DECIMAL(10,2),
            max_profit_reached DECIMAL(10,2) DEFAULT 0,
            protection_mode_enabled BOOLEAN DEFAULT FALSE,
            enabled BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 插入测试配置
    c.execute("""
        INSERT OR REPLACE INTO total_floating_pl_rules 
        (id, profit_threshold, loss_threshold, target_profit_threshold, 
         protection_profit_threshold, max_profit_reached, protection_mode_enabled, enabled)
        VALUES (1, 1000, -500, 2000, 500, 0, 1, 1)
    """)
    
    conn.commit()
    conn.close()
    print("测试数据库设置完成")

def test_scenario_1():
    """测试场景1: 达到目标盈利立即平仓"""
    print("\n=== 测试场景1: 达到目标盈利立即平仓 ===")
    
    # 模拟持仓数据
    positions = [
        {'ticket': 1001, 'profit': 800},
        {'ticket': 1002, 'profit': 600},
        {'ticket': 1003, 'profit': 700}  # 总盈利 = 2100，超过目标2000
    ]
    
    # 模拟规则
    total_rule = {
        'enabled': True,
        'profit_threshold': 1000,
        'loss_threshold': -500,
        'target_profit_threshold': 2000,
        'protection_profit_threshold': 500,
        'max_profit_reached': 0,
        'protection_mode_enabled': True
    }
    
    total_profit = sum(pos['profit'] for pos in positions)
    print(f"当前总盈利: {total_profit}")
    print(f"目标盈利: {total_rule['target_profit_threshold']}")
    
    # 检查逻辑
    if total_profit >= total_rule['target_profit_threshold']:
        print("✅ 触发条件: 达到目标盈利，应该立即平仓所有订单")
        return True
    else:
        print("❌ 未触发目标盈利条件")
        return False

def test_scenario_2():
    """测试场景2: 盈利保护触发"""
    print("\n=== 测试场景2: 盈利保护触发 ===")
    
    # 模拟持仓数据 - 当前盈利回落到保护水平
    positions = [
        {'ticket': 1001, 'profit': 200},
        {'ticket': 1002, 'profit': 150},
        {'ticket': 1003, 'profit': 150}  # 总盈利 = 500，等于保护水平
    ]
    
    # 模拟规则 - 历史最大盈利已达到目标
    total_rule = {
        'enabled': True,
        'profit_threshold': 1000,
        'loss_threshold': -500,
        'target_profit_threshold': 2000,
        'protection_profit_threshold': 500,
        'max_profit_reached': 2100,  # 历史最大盈利超过目标
        'protection_mode_enabled': True
    }
    
    total_profit = sum(pos['profit'] for pos in positions)
    current_max_profit = total_rule['max_profit_reached']
    
    print(f"当前总盈利: {total_profit}")
    print(f"历史最大盈利: {current_max_profit}")
    print(f"目标盈利: {total_rule['target_profit_threshold']}")
    print(f"保护盈利水平: {total_rule['protection_profit_threshold']}")
    
    # 检查保护逻辑
    if (current_max_profit >= total_rule['target_profit_threshold'] and 
        total_profit <= total_rule['protection_profit_threshold']):
        print("✅ 触发条件: 盈利保护触发，应该平仓所有订单保护盈利")
        return True
    else:
        print("❌ 未触发盈利保护条件")
        return False

def test_scenario_3():
    """测试场景3: 正常监控，无触发条件"""
    print("\n=== 测试场景3: 正常监控，无触发条件 ===")
    
    # 模拟持仓数据 - 盈利在正常范围内
    positions = [
        {'ticket': 1001, 'profit': 300},
        {'ticket': 1002, 'profit': 400},
        {'ticket': 1003, 'profit': 500}  # 总盈利 = 1200
    ]
    
    # 模拟规则
    total_rule = {
        'enabled': True,
        'profit_threshold': 1500,
        'loss_threshold': -500,
        'target_profit_threshold': 2000,
        'protection_profit_threshold': 500,
        'max_profit_reached': 1200,
        'protection_mode_enabled': True
    }
    
    total_profit = sum(pos['profit'] for pos in positions)
    print(f"当前总盈利: {total_profit}")
    print(f"历史最大盈利: {total_rule['max_profit_reached']}")
    
    # 检查各种条件
    target_reached = total_profit >= total_rule['target_profit_threshold']
    protection_triggered = (total_rule['max_profit_reached'] >= total_rule['target_profit_threshold'] and 
                           total_profit <= total_rule['protection_profit_threshold'])
    basic_profit_reached = total_profit >= total_rule['profit_threshold']
    
    if not (target_reached or protection_triggered or basic_profit_reached):
        print("✅ 正确: 无触发条件，继续监控")
        return True
    else:
        print("❌ 错误: 不应该触发任何平仓条件")
        return False

def test_scenario_4():
    """测试场景4: 最大盈利更新逻辑"""
    print("\n=== 测试场景4: 最大盈利更新逻辑 ===")
    
    # 模拟盈利增长过程
    profit_sequence = [800, 1200, 1800, 2200, 1900, 1500, 600]
    max_profit = 0
    
    print("盈利变化过程:")
    for i, current_profit in enumerate(profit_sequence):
        if current_profit > max_profit:
            max_profit = current_profit
            print(f"步骤{i+1}: 当前盈利={current_profit}, 更新最大盈利={max_profit}")
        else:
            print(f"步骤{i+1}: 当前盈利={current_profit}, 最大盈利保持={max_profit}")
    
    print(f"✅ 最大盈利正确更新到: {max_profit}")
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始盈利保护功能测试...")
    
    setup_test_database()
    
    test_results = []
    test_results.append(("场景1: 达到目标盈利", test_scenario_1()))
    test_results.append(("场景2: 盈利保护触发", test_scenario_2()))
    test_results.append(("场景3: 正常监控", test_scenario_3()))
    test_results.append(("场景4: 最大盈利更新", test_scenario_4()))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 个测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！盈利保护功能实现正确。")
    else:
        print("⚠️  部分测试失败，需要检查实现逻辑。")
    
    return passed == len(test_results)

if __name__ == "__main__":
    run_all_tests()
